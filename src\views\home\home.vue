<template>
  <div class="snow-page">
    <div class="home-page">
      <!-- 主标题区域 -->
      <div class="header-section">
        <h1 class="main-title">数据赋能教与学 开启教育新时代</h1>
        <p class="subtitle">
          数据与AI技术深度融合，创新教育理论，推动教育全面发展。<br />
          为教师提供强大数字工具，为学习者打造个性化、高效学习环境。
        </p>
      </div>

      <!-- 功能模块区域 -->
      <div class="function-modules">
        <div class="module-grid">
          <div
            v-for="(module, index) in functionModules"
            :key="module.id"
            class="module-card"
            :class="`animated-fade-up-${index}`"
          >
            <div class="card-content">
              <div class="icon-container">
                <s-svg-icon :name="module.icon" :size="48" />
              </div>
              <h3 class="module-title">{{ module.name }}</h3>
              <p class="module-description">{{ module.description }}</p>
              <div class="card-arrow">
                <icon-right />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧功能区域 -->
      <div class="side-functions">
        <div class="side-card">
          <h3 class="side-title">作业测评</h3>
          <p class="side-description">实用简单明朗的测评功能，无需一次测试，提供批改功能</p>
          <div class="side-links">
            <div class="link-item">
              <icon-mobile />
              <span>手机扫描测评</span>
              <icon-right />
            </div>
            <div class="link-item">
              <icon-question-circle />
              <span>问卷批测评</span>
              <icon-right />
            </div>
            <div class="link-item">
              <icon-file-text />
              <span>题题批测评</span>
              <icon-right />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 功能模块数据
const functionModules = ref([
  {
    id: 1,
    name: "智能阅卷",
    description: "采用智能阅卷的高效功能，无需一次测试，提供批改功能",
    icon: "data-analysis"
  },
  {
    id: 2,
    name: "考试阅卷",
    description: "简约支持功能，手机扫描测评功能，考试阅卷功能，高效简约",
    icon: "form"
  },
  {
    id: 3,
    name: "学情分析",
    description: "多维度、多维度、多角度、多形式的学情数据分析，让学习更高效",
    icon: "data"
  },
  {
    id: 4,
    name: "作业批改无人机",
    description: "机器智能批改功能，生成作文智能批改综合功能",
    icon: "functions"
  },
  {
    id: 5,
    name: "五段错题本",
    description: "学生错题目、题、题、题、错题错题，设置全部功能",
    icon: "classify"
  },
  {
    id: 6,
    name: "选题组卷",
    description: "一键选题、智能组卷题，为教师们提供了强大的教学功能",
    icon: "table"
  },
  {
    id: 7,
    name: "基础信息设置",
    description: "班级信息、班级、科目、学生、教师等基础信息全面设置",
    icon: "set"
  }
]);
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  .header-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    margin: 60px auto;
    text-align: center;
    .main-title {
      margin-bottom: 24px;
      font-size: 36px;
      font-weight: 700;
      line-height: 1.3;
      color: #1a1a1a;
      letter-spacing: 1px;
    }
    .subtitle {
      max-width: 700px;
      margin: 0 auto;
      font-size: 18px;
      font-weight: 400;
      line-height: 1.8;
      color: #555555;
    }
  }
  .function-modules {
    margin-bottom: 40px;
    .module-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }
    .module-card {
      position: relative;
      padding: 24px;
      overflow: hidden;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgb(0 0 0 / 8%);
      transition: all 0.3s ease;
      &:hover {
        box-shadow: 0 8px 30px rgb(0 0 0 / 12%);
        transform: translateY(-4px);
      }
      .card-content {
        position: relative;
        z-index: 2;
        .icon-container {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80px;
          height: 80px;
          margin: 0 auto 16px;
          color: white;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 20px;
        }
        .module-title {
          margin-bottom: 8px;
          font-size: 18px;
          font-weight: 600;
          color: #1a1a1a;
          text-align: center;
        }
        .module-description {
          margin-bottom: 16px;
          font-size: 14px;
          line-height: 1.5;
          color: #666666;
          text-align: center;
        }
        .card-arrow {
          position: absolute;
          right: 16px;
          bottom: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          color: #666666;
          background: #f0f2f5;
          border-radius: 50%;
          transition: all 0.3s ease;
        }
      }
      &:hover .card-arrow {
        color: white;
        background: #1890ff;
      }
    }
  }
  .side-functions {
    max-width: 400px;
    margin: 0 auto;
    .side-card {
      padding: 24px;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgb(0 0 0 / 8%);
      .side-title {
        margin-bottom: 8px;
        font-size: 20px;
        font-weight: 600;
        color: #1a1a1a;
      }
      .side-description {
        margin-bottom: 20px;
        font-size: 14px;
        line-height: 1.5;
        color: #666666;
      }
      .side-links {
        .link-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          cursor: pointer;
          border-bottom: 1px solid #f0f0f0;
          transition: all 0.3s ease;
          &:last-child {
            border-bottom: none;
          }
          &:hover {
            padding: 12px;
            margin: 0 -12px;
            background: #f8f9fa;
            border-radius: 8px;
          }
          span {
            flex: 1;
            margin-left: 12px;
            font-size: 14px;
            color: #333333;
          }
          .arco-icon {
            font-size: 16px;
            color: #666666;
          }
        }
      }
    }
  }
}

// 动画效果
@for $i from 0 through 10 {
  .animated-fade-up-#{$i} {
    animation: fade-in-up 0.6s ease-out #{$i * 0.1}s both;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (width <= 768px) {
  .home-page {
    padding: 16px;
    .header-section {
      padding: 20px 10px;
      margin: 40px auto;
      .main-title {
        margin-bottom: 16px;
        font-size: 28px;
        letter-spacing: 0.5px;
      }
      .subtitle {
        max-width: 90%;
        font-size: 16px;
      }
    }
    .function-modules {
      .module-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
      .module-card {
        padding: 20px;
      }
    }
  }
}
</style>
